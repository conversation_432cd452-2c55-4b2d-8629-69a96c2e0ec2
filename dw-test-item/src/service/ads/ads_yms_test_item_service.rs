use crate::config::DwTestItemConfig;
use bumpalo::Bump;
use common::ads::sink::test_item_bin_handler::TestItemBinHandler;
use common::ads::sink::test_item_program_handler::TestItemProgramHandler;
use common::ads::sink::test_item_site_bin_handler::TestItemSiteBinHandler;
use common::ads::sink::test_item_site_handler::TestItemSiteHandler;
use common::ck::ck_sink::SinkHandler;
use common::dto::ads::key::test_item_bin_key::TestItemBinKey;
use common::dto::ads::key::test_item_program_key::TestItemProgramKey;
use common::dto::ads::key::test_item_site_bin_key::TestItemSiteBinKey;
use common::dto::ads::key::test_item_site_key::TestItemSiteKey;
use common::dto::ads::value::test_item_bin::TestItemBin;
use common::dto::ads::value::test_item_detail::TestItemDetail;
use common::dto::ads::value::test_item_program::TestItemProgram;
use common::dto::ads::value::test_item_site::TestItemSite;
use common::dto::ads::value::test_item_site_bin::TestItemSiteBin;
use common::dto::dwd::file_detail::FileDetail;
use common::dto::dwd::sub_test_item_detail::SubTestItemDetail;
use common::dto::ods::product_config::OdsProductConfig;
use common::model::constant::P;
use dashmap::DashMap;
use rayon::prelude::*;
use std::collections::HashMap;
use std::error::Error;
use std::sync::{Arc, Mutex};

/// Grouped test item details for different aggregation levels
/// Contains both total and pass-only groups for performance optimization
#[derive(Debug)]
struct GroupedTestItemDetails {
    /// Groups for TestItemProgram aggregation (by program key) - all records
    program_groups_total: HashMap<TestItemProgramKey, Vec<Arc<TestItemDetail>>>,
    /// Groups for TestItemProgram aggregation (by program key) - pass-only records
    program_groups_pass: HashMap<TestItemProgramKey, Vec<Arc<TestItemDetail>>>,

    /// Groups for TestItemSite aggregation (by site key) - all records
    site_groups_total: HashMap<TestItemSiteKey, Vec<Arc<TestItemDetail>>>,
    /// Groups for TestItemSite aggregation (by site key) - pass-only records
    site_groups_pass: HashMap<TestItemSiteKey, Vec<Arc<TestItemDetail>>>,

    /// Groups for TestItemBin aggregation (by bin key) - all records
    bin_groups_total: HashMap<TestItemBinKey, Vec<Arc<TestItemDetail>>>,
    /// Groups for TestItemBin aggregation (by bin key) - pass-only records
    bin_groups_pass: HashMap<TestItemBinKey, Vec<Arc<TestItemDetail>>>,

    /// Groups for TestItemSiteBin aggregation (by site-bin key) - all records
    site_bin_groups_total: HashMap<TestItemSiteBinKey, Vec<Arc<TestItemDetail>>>,
    /// Groups for TestItemSiteBin aggregation (by site-bin key) - pass-only records
    site_bin_groups_pass: HashMap<TestItemSiteBinKey, Vec<Arc<TestItemDetail>>>,
}

impl GroupedTestItemDetails {
    /// 创建空的分组结果
    fn new() -> Self {
        Self {
            program_groups_total: HashMap::new(),
            program_groups_pass: HashMap::new(),
            site_groups_total: HashMap::new(),
            site_groups_pass: HashMap::new(),
            bin_groups_total: HashMap::new(),
            bin_groups_pass: HashMap::new(),
            site_bin_groups_total: HashMap::new(),
            site_bin_groups_pass: HashMap::new(),
        }
    }
}

/// ADS YMS Test Item Service handles transformation from DWD to ADS layer
/// This service processes test item data for YMS (Yield Management System) analytics
///
/// Corresponds to the ADS layer processing in the original Scala implementation
#[derive(Debug, Clone)]
pub struct AdsYmsTestItemService {
    /// Configuration properties for the service
    properties: DwTestItemConfig,
}

impl AdsYmsTestItemService {
    pub fn new(properties: DwTestItemConfig) -> Self {
        Self { properties }
    }

    /// Calculate test item processing - main entry point
    ///
    /// Corresponds to the Scala calculateTestItem method:
    /// def calculateTestItem(spark: SparkSession, subTestItem: Dataset[SubTestItemDetail],
    ///                      fileDetailMap: Broadcast[Map[Long, FileDetail]],
    ///                      productList: Broadcast[List[OdsProductConfig]],
    ///                      dieFinalInfo: Broadcast[List[DieFinalInfo]],
    ///                      version: Long, itemHasFinalInfo: Boolean): Unit
    pub async fn calculate_test_item(
        &self,
        sub_test_item: &Vec<Vec<SubTestItemDetail>>,
        file_detail_map: &HashMap<i64, FileDetail>,
        product_list: &[OdsProductConfig],
        die_final_info: &[common::dto::ads::value::DieFinalInfo],
        version: i64,
        item_has_final_info: bool,
    ) -> Result<(), Box<dyn Error + Send + Sync>> {
        log::info!("开始计算 ADS YMS Test Item...");
        // Transform SubTestItemDetail to TestItemDetail
        let test_item_details: Vec<Arc<TestItemDetail>> =
            // Standard build without die final info
            sub_test_item
                .par_iter()
                .flatten()
                .into_par_iter()
                .filter_map(|elem| {
                    if let Some(file_detail) = file_detail_map.get(&elem.FILE_ID?) {
                        if item_has_final_info {
                            Some(Arc::new(TestItemDetail::build_test_item_with_die_final_info(
                                &elem,
                                file_detail,
                                version,
                                die_final_info,
                            )))
                        } else {
                            Some(Arc::new(TestItemDetail::build_test_item(&elem, file_detail, version)))
                        }
                    } else {
                        log::warn!("File detail not found for FILE_ID: {:?}", elem.FILE_ID);
                        None
                    }
                })
                .collect();
        log::info!("转换Ads TestItemDetail完成，共 {} 条数据", test_item_details.len());
        // Calculate all four aggregation results
        let (test_item_programs, test_item_sites, test_item_bins, test_item_site_bins) = self
            .calculate_all(test_item_details, product_list)
            .map_err(|e| -> Box<dyn Error + Send + Sync> {
                Box::new(std::io::Error::new(std::io::ErrorKind::Other, e.to_string()))
            })?;
        log::info!("yms ads calculate_all完成.");
        // Create handlers for each table
        let test_item_program_handler = TestItemProgramHandler::new(self.properties.ads_db_name.clone());
        let test_item_site_handler = TestItemSiteHandler::new(self.properties.ads_db_name.clone());
        let test_item_bin_handler = TestItemBinHandler::new(self.properties.ads_db_name.clone());
        let test_item_site_bin_handler = TestItemSiteBinHandler::new(self.properties.ads_db_name.clone());

        // Execute async writes to ClickHouse in parallel using tokio::join!
        let (result1, result2, result3, result4) = tokio::join!(
            self.write_to_ck_generic(test_item_programs, test_item_program_handler),
            self.write_to_ck_generic(test_item_sites, test_item_site_handler),
            self.write_to_ck_generic(test_item_bins, test_item_bin_handler),
            self.write_to_ck_generic(test_item_site_bins, test_item_site_bin_handler)
        );

        // Check for any errors
        result1?;
        result2?;
        result3?;
        result4?;

        Ok(())
    }

    /// Generic helper method for async ClickHouse writes
    ///
    /// This method handles the async writing to ClickHouse with proper error handling
    /// and corresponds to the writeToCk calls in the original Scala implementation
    async fn write_to_ck_generic<T>(
        &self,
        data: Vec<T>,
        handler: impl SinkHandler + Send + Sync,
    ) -> Result<(), Box<dyn Error + Send + Sync>>
    where
        T: clickhouse::Row + serde::Serialize + Send + Sync + Clone + 'static,
    {
        use ck_provider::CkConfig;
        use common::ck::ck_sink::CkSink;

        if data.is_empty() {
            log::info!("No data to write.");
            return Ok(());
        }

        log::info!("Writing {} records to ClickHouse.", data.len());

        let ck_config = self.properties.get_ck_config(self.properties.ads_db_name.as_str());

        // Write to ClickHouse with partition
        CkSink::write_to_ck(&data, 1, &ck_config, &handler, false).await.map_err(
            |e| -> Box<dyn Error + Send + Sync> {
                log::error!("写入clickhouse 失败: {}, 数据量为: {}", handler.table_name(), data.len());
                Box::new(std::io::Error::new(std::io::ErrorKind::Other, e.to_string()))
            },
        )?;

        log::info!("Successfully wrote {} records to ClickHouse.", data.len());
        Ok(())
    }

    /// Calculate all ADS test item aggregations with multi-threading support
    ///
    /// This method replicates the Scala calculateAll functionality:
    /// def calculateAll(spark: SparkSession, testItemDetail: Dataset[TestItemDetail],
    ///                  productList: Broadcast[List[OdsProductConfig]]):
    ///                  (Dataset[TestItemProgram], Dataset[TestItemSite], Dataset[TestItemBin], Dataset[TestItemSiteBin])
    pub fn calculate_all(
        &self,
        test_item_details: Vec<Arc<TestItemDetail>>,
        product_list: &[OdsProductConfig],
    ) -> Result<(Vec<TestItemProgram>, Vec<TestItemSite>, Vec<TestItemBin>, Vec<TestItemSiteBin>), Box<dyn Error>> {
        let start_time = std::time::Instant::now();
        let bump = Bump::new();
        let grouped_data = bump.alloc(self.group_test_item_details_concurrent(test_item_details)?);
        let group_duration = start_time.elapsed();
        log::info!("分组耗时: {:?}", group_duration);

        // 并行处理四种聚合类型
        let ((programs, sites), (bins, site_bins)) = rayon::join(
            || {
                rayon::join(
                    || self.process_program_groups(&grouped_data, product_list),
                    || self.process_site_groups(&grouped_data, product_list),
                )
            },
            || {
                rayon::join(
                    || self.process_bin_groups(&grouped_data, product_list),
                    || self.process_site_bin_groups(&grouped_data, product_list),
                )
            },
        );

        drop(bump);
        Ok((programs, sites, bins, site_bins))
    }

    /// 使用 DashMap + Mutex 的并发分组方法
    /// 直接返回单一的 GroupedTestItemDetails，避免后续合并操作
    fn group_test_item_details_concurrent(
        &self,
        test_item_details: Vec<Arc<TestItemDetail>>,
    ) -> Result<GroupedTestItemDetails, Box<dyn Error>> {
        // 使用 DashMap 和 Mutex<Vec> 来支持并发写入
        let program_groups_total: DashMap<TestItemProgramKey, Mutex<Vec<Arc<TestItemDetail>>>> = DashMap::new();
        let program_groups_pass: DashMap<TestItemProgramKey, Mutex<Vec<Arc<TestItemDetail>>>> = DashMap::new();
        let site_groups_total: DashMap<TestItemSiteKey, Mutex<Vec<Arc<TestItemDetail>>>> = DashMap::new();
        let site_groups_pass: DashMap<TestItemSiteKey, Mutex<Vec<Arc<TestItemDetail>>>> = DashMap::new();
        let bin_groups_total: DashMap<TestItemBinKey, Mutex<Vec<Arc<TestItemDetail>>>> = DashMap::new();
        let bin_groups_pass: DashMap<TestItemBinKey, Mutex<Vec<Arc<TestItemDetail>>>> = DashMap::new();
        let site_bin_groups_total: DashMap<TestItemSiteBinKey, Mutex<Vec<Arc<TestItemDetail>>>> = DashMap::new();
        let site_bin_groups_pass: DashMap<TestItemSiteBinKey, Mutex<Vec<Arc<TestItemDetail>>>> = DashMap::new();

        // 并行处理每个 TestItemDetail
        test_item_details.par_iter().for_each(|detail| {
            // Create keys for each aggregation level
            let program_key = TestItemProgramKey::from_test_item_detail(&detail);
            let site_key = TestItemSiteKey::from_test_item_detail(&detail);
            let bin_key = TestItemBinKey::from_test_item_detail(&detail);
            let site_bin_key = TestItemSiteBinKey::from_test_item_detail(&detail);

            // Check if this is a pass record
            let is_pass = detail.HBIN_PF.as_ref() == P;

            // Add to pass-only groups if this is a pass record
            if is_pass {
                program_groups_pass
                    .entry(program_key.clone())
                    .or_insert_with(|| Mutex::new(Vec::new()))
                    .lock()
                    .unwrap()
                    .push(detail.clone());
                site_groups_pass
                    .entry(site_key.clone())
                    .or_insert_with(|| Mutex::new(Vec::new()))
                    .lock()
                    .unwrap()
                    .push(detail.clone());
                bin_groups_pass
                    .entry(bin_key.clone())
                    .or_insert_with(|| Mutex::new(Vec::new()))
                    .lock()
                    .unwrap()
                    .push(detail.clone());
                site_bin_groups_pass
                    .entry(site_bin_key.clone())
                    .or_insert_with(|| Mutex::new(Vec::new()))
                    .lock()
                    .unwrap()
                    .push(detail.clone());
            }
            // Add to total groups (all records)
            program_groups_total
                .entry(program_key)
                .or_insert_with(|| Mutex::new(Vec::new()))
                .lock()
                .unwrap()
                .push(detail.clone());
            site_groups_total
                .entry(site_key)
                .or_insert_with(|| Mutex::new(Vec::new()))
                .lock()
                .unwrap()
                .push(detail.clone());
            bin_groups_total
                .entry(bin_key)
                .or_insert_with(|| Mutex::new(Vec::new()))
                .lock()
                .unwrap()
                .push(detail.clone());
            site_bin_groups_total
                .entry(site_bin_key)
                .or_insert_with(|| Mutex::new(Vec::new()))
                .lock()
                .unwrap()
                .push(detail.clone());
        });

        // 转换 DashMap<Key, Mutex<Vec<_>>> 为 HashMap<Key, Vec<_>>
        Ok(GroupedTestItemDetails {
            program_groups_total: program_groups_total
                .into_iter()
                .map(|(k, mutex_vec)| {
                    let vec = mutex_vec.into_inner().unwrap();
                    (k, vec)
                })
                .collect(),
            program_groups_pass: program_groups_pass
                .into_iter()
                .map(|(k, mutex_vec)| {
                    let vec = mutex_vec.into_inner().unwrap();
                    (k, vec)
                })
                .collect(),
            site_groups_total: site_groups_total
                .into_iter()
                .map(|(k, mutex_vec)| {
                    let vec = mutex_vec.into_inner().unwrap();
                    (k, vec)
                })
                .collect(),
            site_groups_pass: site_groups_pass
                .into_iter()
                .map(|(k, mutex_vec)| {
                    let vec = mutex_vec.into_inner().unwrap();
                    (k, vec)
                })
                .collect(),
            bin_groups_total: bin_groups_total
                .into_iter()
                .map(|(k, mutex_vec)| {
                    let vec = mutex_vec.into_inner().unwrap();
                    (k, vec)
                })
                .collect(),
            bin_groups_pass: bin_groups_pass
                .into_iter()
                .map(|(k, mutex_vec)| {
                    let vec = mutex_vec.into_inner().unwrap();
                    (k, vec)
                })
                .collect(),
            site_bin_groups_total: site_bin_groups_total
                .into_iter()
                .map(|(k, mutex_vec)| {
                    let vec = mutex_vec.into_inner().unwrap();
                    (k, vec)
                })
                .collect(),
            site_bin_groups_pass: site_bin_groups_pass
                .into_iter()
                .map(|(k, mutex_vec)| {
                    let vec = mutex_vec.into_inner().unwrap();
                    (k, vec)
                })
                .collect(),
        })
    }

    /// 直接处理Program聚合组（无需合并）
    fn process_program_groups(
        &self,
        grouped_data: &GroupedTestItemDetails,
        product_list: &[OdsProductConfig],
    ) -> Vec<TestItemProgram> {
        let (total, pass_only) = rayon::join(
            || self.aggregate_program_groups(&grouped_data.program_groups_total, 0, product_list),
            || self.aggregate_program_groups(&grouped_data.program_groups_pass, 1, product_list),
        );
        log::info!("Ads yms program aggregation completed, total: {}, pass_only: {}.", total.len(), pass_only.len());
        [total, pass_only].concat()
    }

    /// 直接处理Site聚合组（无需合并）
    fn process_site_groups(
        &self,
        grouped_data: &GroupedTestItemDetails,
        product_list: &[OdsProductConfig],
    ) -> Vec<TestItemSite> {
        let (total, pass_only) = rayon::join(
            || self.aggregate_site_groups(&grouped_data.site_groups_total, 0, product_list),
            || self.aggregate_site_groups(&grouped_data.site_groups_pass, 1, product_list),
        );
        log::info!("Ads yms site aggregation completed, total: {}, pass_only: {}.", total.len(), pass_only.len());
        [total, pass_only].concat()
    }

    /// 直接处理Bin聚合组（无需合并）
    fn process_bin_groups(
        &self,
        grouped_data: &GroupedTestItemDetails,
        product_list: &[OdsProductConfig],
    ) -> Vec<TestItemBin> {
        let (total, pass_only) = rayon::join(
            || self.aggregate_bin_groups(&grouped_data.bin_groups_total, 0, product_list),
            || self.aggregate_bin_groups(&grouped_data.bin_groups_pass, 1, product_list),
        );
        log::info!("Ads yms bin aggregation completed, total: {}, pass_only: {}.", total.len(), pass_only.len());
        [total, pass_only].concat()
    }

    /// 直接处理SiteBin聚合组（无需合并）
    fn process_site_bin_groups(
        &self,
        grouped_data: &GroupedTestItemDetails,
        product_list: &[OdsProductConfig],
    ) -> Vec<TestItemSiteBin> {
        let (total, pass_only) = rayon::join(
            || self.aggregate_site_bin_groups(&grouped_data.site_bin_groups_total, 0, product_list),
            || self.aggregate_site_bin_groups(&grouped_data.site_bin_groups_pass, 1, product_list),
        );
        log::info!("Ads yms site bin aggregation completed, total: {}, pass_only: {}.", total.len(), pass_only.len());
        [total, pass_only].concat()
    }

    /// 直接聚合Program组（无需reduce操作）
    fn aggregate_program_groups(
        &self,
        groups: &HashMap<TestItemProgramKey, Vec<Arc<TestItemDetail>>>,
        is_pass_only: u8,
        product_list: &[OdsProductConfig],
    ) -> Vec<TestItemProgram> {
        let start_time = std::time::Instant::now();
        let result = groups
            .par_iter()
            .map(|(_k, items)| TestItemProgram::from_test_items(items, is_pass_only, product_list))
            .collect();
        let duration = start_time.elapsed();
        log::info!(
            "Program direct aggregation took: {:?}, is_pass_only: {}, groups size: {}",
            duration,
            is_pass_only,
            groups.len()
        );
        result
    }

    /// 直接聚合Site组（无需reduce操作）
    fn aggregate_site_groups(
        &self,
        groups: &HashMap<TestItemSiteKey, Vec<Arc<TestItemDetail>>>,
        is_pass_only: u8,
        product_list: &[OdsProductConfig],
    ) -> Vec<TestItemSite> {
        let start_time = std::time::Instant::now();
        let result = groups
            .par_iter()
            .map(|(_k, items)| TestItemSite::from_test_items(items, is_pass_only, product_list))
            .collect();
        let duration = start_time.elapsed();
        log::info!(
            "Site direct aggregation took: {:?}, is_pass_only: {}, groups size: {}",
            duration,
            is_pass_only,
            groups.len()
        );
        result
    }

    /// 直接聚合Bin组（无需reduce操作）
    fn aggregate_bin_groups(
        &self,
        groups: &HashMap<TestItemBinKey, Vec<Arc<TestItemDetail>>>,
        is_pass_only: u8,
        product_list: &[OdsProductConfig],
    ) -> Vec<TestItemBin> {
        let start_time = std::time::Instant::now();
        let result = groups
            .par_iter()
            .map(|(_k, items)| TestItemBin::from_test_items(items, is_pass_only, product_list))
            .collect();
        let duration = start_time.elapsed();
        log::info!(
            "Bin direct aggregation took: {:?}, is_pass_only: {}, groups size: {}",
            duration,
            is_pass_only,
            groups.len()
        );
        result
    }

    /// 直接聚合SiteBin组（无需reduce操作）
    fn aggregate_site_bin_groups(
        &self,
        groups: &HashMap<TestItemSiteBinKey, Vec<Arc<TestItemDetail>>>,
        is_pass_only: u8,
        product_list: &[OdsProductConfig],
    ) -> Vec<TestItemSiteBin> {
        let start_time = std::time::Instant::now();
        let result = groups
            .par_iter()
            .map(|(_k, items)| TestItemSiteBin::from_test_items(items, is_pass_only, product_list))
            .collect();
        let duration = start_time.elapsed();
        log::info!(
            "SiteBin direct aggregation took: {:?}, is_pass_only: {}, groups size: {}",
            duration,
            is_pass_only,
            groups.len()
        );
        result
    }
}
